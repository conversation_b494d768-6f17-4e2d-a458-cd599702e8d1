import { StepIndicator } from "@/components/onboarding/StepIndicator";
import { BusinessProfileStep } from "@/components/onboarding/steps/BusinessProfileStep";
import { FarmOperationsStep } from "@/components/onboarding/steps/FarmOperationsStep";
import { FinancialSystemsStep } from "@/components/onboarding/steps/FinancialSystemsStep";
import { RegistrationsInsuranceStep } from "@/components/onboarding/steps/RegistrationsInsuranceStep";
import { FinalizationStep } from "@/components/onboarding/steps/FinalizationStep";
import Header from "@/components/landing/Header";
import { Card, CardContent } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { OnboardingNavigation } from "@/components/onboarding/OnboardingNavigation";
import { Button } from "@/components/ui/button";

// Placeholder components for new 5-step structure
// TODO: These will be replaced with full implementations in Phase 2
const RegistrationsInsuranceStep = () => (
  <div className="space-y-6">
    <div className="text-center">
      <h2 className="text-2xl font-bold mb-2">Registrations & Insurance</h2>
      <p className="text-muted-foreground">
        Vehicle registrations, insurance policies, and enhanced licenses management.
      </p>
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          🚧 This step is under development. Full functionality will be available soon.
        </p>
      </div>
    </div>
  </div>
);



/**
 * Enhanced Step Configuration System - Phase 1 Architecture Modernization
 *
 * `STEP_CONFIG` defines the comprehensive configuration for each step in the onboarding wizard.
 * Each step configuration includes:
 * - `id`: Unique numerical identifier for the step
 * - `title`: Display title shown in the StepIndicator
 * - `component`: React component to render for this step
 * - `tables`: Database tables associated with this step (for validation and dependency checking)
 * - `dependencies`: Steps that must be completed before this step can be accessed
 * - `validation`: Step-specific validation configuration
 * - `progressGate`: Completion requirements for this step
 */
interface StepConfig {
  id: number;
  title: string;
  component: React.ComponentType;
  tables: string[];
  dependencies?: number[];
  validation?: {
    required: boolean;
    customValidator?: (sessionData: any) => { isValid: boolean; errors: string[] };
  };
  progressGate?: {
    minCompletionPercentage: number;
    requiredFields: string[];
  };
}

const STEP_CONFIG: StepConfig[] = [
  {
    id: 1,
    title: "Business Profile",
    component: BusinessProfileStep,
    tables: ['business_registration', 'contacts', 'addresses', 'key_staff'],
    validation: {
      required: true,
      customValidator: (sessionData) => {
        // Business profile validation logic
        const step1Data = sessionData?.step1_businessProfile;
        const errors: string[] = [];

        if (!step1Data?.businessRegistration?.business_name) {
          errors.push('Business name is required');
        }
        if (!step1Data?.contacts?.length) {
          errors.push('At least one contact is required');
        }

        return { isValid: errors.length === 0, errors };
      }
    },
    progressGate: {
      minCompletionPercentage: 80,
      requiredFields: ['business_name', 'abn', 'primary_contact']
    }
  },
  {
    id: 2,
    title: "Farm Operations",
    component: FarmOperationsStep,
    tables: ['activities', 'licenses', 'suppliers', 'contracts', 'chemical_usage'],
    dependencies: [1],
    validation: {
      required: true,
      customValidator: (sessionData) => {
        const step2Data = sessionData?.step2_farmOperations;
        const errors: string[] = [];

        if (!step2Data?.activities?.length) {
          errors.push('At least one farm activity is required');
        }

        return { isValid: errors.length === 0, errors };
      }
    },
    progressGate: {
      minCompletionPercentage: 70,
      requiredFields: ['primary_activities']
    }
  },
  {
    id: 3,
    title: "Financial Systems",
    component: FinancialSystemsStep,
    tables: ['bookkeeping', 'payroll', 'assets'],
    dependencies: [1, 2],
    validation: {
      required: true
    },
    progressGate: {
      minCompletionPercentage: 60,
      requiredFields: ['bookkeeping_method']
    }
  },
  {
    id: 4,
    title: "Registrations & Insurance",
    component: RegistrationsInsuranceStep,
    tables: ['vehicle_registrations', 'insurance_policies', 'licenses_new'],
    dependencies: [1, 2, 3],
    validation: {
      required: false // Optional step
    },
    progressGate: {
      minCompletionPercentage: 50,
      requiredFields: []
    }
  },
  {
    id: 5,
    title: "Finalization",
    component: FinalizationStep,
    tables: ['permissions', 'agreements', 'payments', 'communication_preferences'],
    dependencies: [1, 2, 3],
    validation: {
      required: true,
      customValidator: (sessionData) => {
        // Comprehensive validation across all steps
        const errors: string[] = [];

        // Validate all required previous steps
        if (!sessionData?.step1_businessProfile?.businessRegistration?.business_name) {
          errors.push('Business profile must be completed');
        }
        if (!sessionData?.step2_farmOperations?.activities?.length) {
          errors.push('Farm operations must be completed');
        }

        return { isValid: errors.length === 0, errors };
      }
    },
    progressGate: {
      minCompletionPercentage: 90,
      requiredFields: ['agreements_accepted', 'payment_method']
    }
  }
];

// Legacy STEPS array for backward compatibility
const STEPS = STEP_CONFIG.map(config => ({
  id: config.id,
  title: config.title,
  component: config.component
}));

/**
 * Step Validation and Dependency Management System
 */
interface StepValidationResult {
  isValid: boolean;
  errors: string[];
  completionPercentage: number;
  canProceed: boolean;
}

const validateStepDependencies = (stepId: number, sessionData: any): boolean => {
  const stepConfig = STEP_CONFIG.find(config => config.id === stepId);
  if (!stepConfig?.dependencies) return true;

  return stepConfig.dependencies.every(depStepId => {
    const depConfig = STEP_CONFIG.find(config => config.id === depStepId);
    if (!depConfig) return false;

    // Check if dependency step has minimum completion
    const validation = validateStep(depStepId, sessionData);
    return validation.completionPercentage >= (depConfig.progressGate?.minCompletionPercentage || 50);
  });
};

const validateStep = (stepId: number, sessionData: any): StepValidationResult => {
  const stepConfig = STEP_CONFIG.find(config => config.id === stepId);
  if (!stepConfig) {
    return { isValid: false, errors: ['Invalid step'], completionPercentage: 0, canProceed: false };
  }

  // Check dependencies first
  const dependenciesMet = validateStepDependencies(stepId, sessionData);
  if (!dependenciesMet) {
    return {
      isValid: false,
      errors: ['Previous steps must be completed first'],
      completionPercentage: 0,
      canProceed: false
    };
  }

  // Run custom validation if available
  if (stepConfig.validation?.customValidator) {
    const customResult = stepConfig.validation.customValidator(sessionData);
    const completionPercentage = customResult.isValid ? 100 :
      Math.max(0, 100 - (customResult.errors.length * 20)); // Rough completion estimate

    return {
      isValid: customResult.isValid,
      errors: customResult.errors,
      completionPercentage,
      canProceed: !stepConfig.validation.required || customResult.isValid
    };
  }

  // Default validation - assume valid if no custom validator
  return { isValid: true, errors: [], completionPercentage: 100, canProceed: true };
};

/**
 * Enhanced Onboarding Component with Step Validation Pipeline
 *
 * Features:
 * - Dynamic step configuration with dependency checking
 * - Progressive disclosure with completion gates
 * - Step-specific validation and error handling
 * - Enhanced navigation with validation feedback
 */
const Onboarding = () => {
  const {
    loading, // Indicates if the OnboardingContext is busy loading initial session data.
    currentStep, // The active step number (1-indexed), managed by OnboardingContext and persisted in `onboarding_sessions.current_step`.
    updateCurrentStep, // Function from OnboardingContext to change the current step in the backend and context state.
    sessionData, // Holds all data for the current onboarding session, fetched via `get_onboarding_session_data` RPC.
    error // Global error messages from OnboardingContext (e.g., failure to load session).
  } = useOnboarding();

  // Display a global loading indicator if the context is loading and no session data is yet available.
  // This typically happens on initial page load while fetching or creating the onboarding session.
  if (loading && !sessionData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
        <p className="ml-4 text-lg">Loading your session...</p>
      </div>
    );
  }

  // Display a global error message if the OnboardingContext encountered a critical error (e.g., failed to load session).
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center text-red-700">
          <h2 className="text-2xl font-bold mb-2">An Error Occurred</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Reload Page
          </Button>
        </div>
      </div>
    );
  }

  // Enhanced navigation with validation
  const currentStepValidation = validateStep(currentStep, sessionData);
  const nextStepValidation = currentStep < STEPS.length ? validateStep(currentStep + 1, sessionData) : null;

  /**
   * Enhanced `handleNext` with validation pipeline
   * Validates current step before allowing navigation to next step
   */
  const handleNext = () => {
    // Validate current step before proceeding
    if (!currentStepValidation.canProceed) {
      // Show validation errors to user
      console.warn('Cannot proceed: Current step validation failed', currentStepValidation.errors);
      return;
    }

    if (currentStep < STEPS.length) {
      // Check if next step dependencies are met
      if (nextStepValidation && !validateStepDependencies(currentStep + 1, sessionData)) {
        console.warn('Cannot proceed: Next step dependencies not met');
        return;
      }

      updateCurrentStep(currentStep + 1);
    } else {
      // Final step - trigger submission logic
      console.log("Onboarding complete! Session Data:", sessionData);
      // TODO: Implement final submission logic
      // 1. Marking the `onboarding_sessions.status` as 'submitted' or 'completed'
      // 2. Calling Edge Function to trigger post-onboarding workflows
      // 3. Navigating to confirmation page or dashboard
    }
  };

  /**
   * Enhanced `handleBack` with validation awareness
   */
  const handleBack = () => {
    if (currentStep > 1) {
      updateCurrentStep(currentStep - 1);
    }
  };

  // Determine navigation state
  const canGoNext = currentStepValidation.canProceed &&
    (currentStep >= STEPS.length || (nextStepValidation && validateStepDependencies(currentStep + 1, sessionData)));
  const canGoBack = currentStep > 1;

  // Dynamically determines which step component to render based on `currentStep`.
  const CurrentStepComponent = STEPS[currentStep - 1]?.component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/5">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* `StepIndicator` visually shows the user's progress through the defined STEPS. */}
          <StepIndicator
            steps={STEPS.map(step => ({ id: step.id, title: step.title }))}
            currentStep={currentStep}
          />

          <Card className="mt-8">
            <CardContent className="p-8">
              {/* Renders the component for the current active step. */}
              {CurrentStepComponent ? <CurrentStepComponent /> : <p>Step not found.</p>}
              {/* Enhanced `OnboardingNavigation` with validation-driven navigation */}
              <OnboardingNavigation
                currentStep={currentStep}
                totalSteps={STEPS.length}
                onNext={handleNext}
                onBack={handleBack}
                canGoBack={canGoBack}
                canGoNext={canGoNext}
              />

              {/* Step Validation Feedback */}
              {!currentStepValidation.isValid && currentStepValidation.errors.length > 0 && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">Step Validation Issues:</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {currentStepValidation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                  <div className="mt-2 text-xs text-yellow-600">
                    Completion: {currentStepValidation.completionPercentage}%
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
