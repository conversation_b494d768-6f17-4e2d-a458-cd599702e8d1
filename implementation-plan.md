# Implementation Plan

### **Phase 1: Foundation & Architecture Modernization**

#### **1.1 Dynamic Step Configuration System**
- **File:** `src/pages/Onboarding.tsx`
  ```typescript
  const STEP_CONFIG = [
    { id: 1, title: "Business Profile", component: BusinessProfileStep, tables: ['business_registration', 'contacts', 'addresses', 'key_staff'] },
    { id: 2, title: "Farm Operations", component: FarmOperationsStep, tables: ['activities', 'suppliers', 'contracts', 'chemical_usage'] },
    { id: 3, title: "Financial Systems", component: FinancialSystemsStep, tables: ['bookkeeping', 'payroll', 'assets'] },
    { id: 4, title: "Registrations & Insurance", component: RegistrationsInsuranceStep, tables: ['vehicle_registrations', 'insurance_policies', 'licenses_new'] },
    { id: 5, title: "Finalization", component: FinalizationStep, tables: ['permissions', 'agreements', 'payments', 'communication_preferences'] }
  ];
  ```
  - Implement step validation pipeline with dependency checking
  - Add progressive disclosure with completion gates
  - Create step-specific loading and error boundaries

#### **1.2 Enhanced Type System Architecture**
- **File:** `src/types/database.types.ts` (auto-regenerated)
- **File:** `src/types/onboarding.ts`
  ```typescript
  interface Step4RegistrationsData {
    vehicleRegistrations: VehicleRegistration[];
    insurancePolicies: InsurancePolicy[];
    licenses: EnhancedLicense[];
  }
  
  interface Step5FinalizationData {
    permissions: Permission[];
    agreements: Agreement[];
    payments: Payment;
    communicationPreferences: CommunicationPreferences;
    finalizationSubmission: FinalizationSubmission;
  }
  ```

#### **1.3 Context System Overhaul**
- **File:** `src/contexts/OnboardingContext.tsx`
  - Implement generic step factory pattern:
  ```typescript
  const ensureStepRecordExists = async (stepNumber: number, sessionId: string) => {
    const stepTable = `step_${stepNumber}_${STEP_CONFIG[stepNumber-1].key}`;
    // Generic implementation
  };
  ```
  - Add step transition validation
  - Implement data consistency checks across step boundaries

### **Phase 2: Component Architecture Excellence**

#### **2.1 Step 4: Comprehensive Registrations & Insurance Management**
- **File:** `src/components/onboarding/steps/RegistrationsInsuranceStep.tsx`
  ```typescript
  export const RegistrationsInsuranceStep = () => {
    const { effectiveStepId } = useEnhancedStepInit({
      stepName: 'RegistrationsInsuranceStep',
      stepNumber: 4,
      ensureStepFunction: ensureStep4RegistrationsInsuranceRecordExists,
    });

    return (
      <StepContainer>
        <TabsContainer defaultValue="vehicles">
          <TabsList>
            <TabsTrigger value="vehicles">Vehicle Registrations</TabsTrigger>
            <TabsTrigger value="insurance">Insurance Policies</TabsTrigger>
            <TabsTrigger value="licenses">Licenses & Permits</TabsTrigger>
          </TabsList>
          
          <TabsContent value="vehicles">
            <EntitySection<VehicleRegistrationFormData>
              title="Vehicle Registrations"
              description="Manage farm vehicle registrations and compliance"
              entityManagement={vehicleManagement}
              FormComponent={VehicleRegistrationForm}
              emptyStateMessage="No vehicles registered yet"
            />
          </TabsContent>
          {/* Similar for insurance and licenses */}
        </TabsContainer>
      </StepContainer>
    );
  };
  ```

#### **2.2 Advanced Form Components with Cross-Integration**
- **File:** `src/components/form_components/VehicleRegistrationForm.tsx`
  ```typescript
  export const VehicleRegistrationForm: EntityFormComponent<VehicleRegistrationFormData> = ({ entity, onUpdate, onDelete }) => {
    const { formData, updateField, errors } = useFormManagement({
      entityId: entity.id,
      tableName: 'vehicle_registrations',
      initialData: entity,
      validationSchema: vehicleRegistrationSchema,
    });

    return (
      <FormCard>
        <FormField label="Vehicle Type" error={errors.vehicle_type}>
          <Select value={formData.vehicle_type} onValueChange={(value) => updateField('vehicle_type', value)}>
            {VEHICLE_TYPES.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
          </Select>
        </FormField>
        
        <ExpiryDateField
          label="Registration Expiry"
          value={formData.registration_expiry}
          onChange={(date) => updateField('registration_expiry', date)}
          alertThresholdDays={30}
        />
        
        <InsuranceLinkingField
          vehicleId={entity.id}
          currentInsurancePolicy={formData.insurance_policy_id}
          onPolicyLink={(policyId) => updateField('insurance_policy_id', policyId)}
        />
      </FormCard>
    );
  };
  ```

- **File:** `src/components/form_components/InsurancePolicyForm.tsx`
  - Multi-type insurance support with conditional fields
  - Premium calculation and renewal tracking
  - Asset linking for comprehensive coverage tracking

- **File:** `src/components/form_components/EnhancedLicenseForm.tsx`
  - Status lifecycle management (Current → Pending Renewal → Expired)
  - Automated renewal reminders
  - Compliance tracking with audit trail

#### **2.3 Step 5: Finalization Excellence**
- **File:** `src/components/onboarding/steps/FinalizationStep.tsx`
  - Update all step_4_id references to step_5_id
  - Add completion validation across all previous steps
  - Implement final submission workflow with comprehensive validation
  - Add submission preview and confirmation system

### **Phase 3: Hook System Mastery**

#### **3.1 Enhanced Step Initialization**
- **File:** `src/hooks/use-enhanced-step-init.ts`
  ```typescript
  export const useEnhancedStepInit = ({ stepName, stepNumber, ensureStepFunction }) => {
    // Add step dependency validation
    // Add step transition guards
  };
  ```

#### **3.2 Specialized Entity Management Enhancement**
- **File:** `src/hooks/use-entity-list-management.ts`
  - Add step-aware entity management
  - Implement cross-entity validation (e.g., vehicle-insurance linking)
  - Add entity lifecycle management (creation, active, archived)


### **Phase 4: Utility System Excellence**

#### **4.1 Enhanced Validation Architecture**
- **File:** `src/utils/onboarding-validation.ts`
  ```typescript
  export const validateStep4 = (step4Data: Step4RegistrationsData): ValidationResult => {
    // Comprehensive Step 4 validation
    // Cross-entity validation (vehicle-insurance linking)
    // Compliance checking (registration expiry, insurance coverage)
  };
  
  export const validateStep5 = (step5Data: Step5FinalizationData): ValidationResult => {
    // Final submission validation
    // All previous steps completion check
    // Legal requirements validation
  };
  ```

#### **4.2 Enhanced Business Constants & Utilities**
- **File:** `src/utils/business-constants.ts`
  ```typescript
  export const STEP_4_CONSTANTS = {
    VEHICLE_TYPES: [...],
    INSURANCE_TYPES: [...],
    LICENSE_STATUSES: [...],
    RENEWAL_FREQUENCIES: [...]
  };
  
  export const CROSS_ENTITY_RULES = {
    vehicleInsuranceRequirements: { /* mapping rules */ },
    licenseRenewalThresholds: { /* alert thresholds */ }
  };
  ```

### **Phase 5: UI/UX Enhancement & Testing**

#### **5.1 Enhanced Step Indicator**
- **File:** `src/components/onboarding/StepIndicator.tsx`
  - Update for 5-step display
  - Add step completion indicators
  - Implement step dependency visualization

#### **5.2 Enhanced Navigation**
- **File:** `src/components/onboarding/OnboardingNavigation.tsx`
  - Add step validation gates
  - Implement smart navigation with completion checking
  - Add progress persistence

#### **5.3 Comprehensive Testing Updates**
- **New File:** `test/registrationsInsuranceStep.test.tsx`
- **New File:** `test/finalizationStep.test.tsx`
- **Update:** All existing test files for 5-step structure

### **Phase 6: Performance & Security Optimization**

#### **6.1 Performance Enhancements**
- Implement step-specific lazy loading
- Add entity virtualization for large lists
- Optimize form auto-save patterns

#### **6.2 Security Updates**
- Update RLS policies for new step structure
- Add step transition authorization

### **Phase 7: Documentation & Deployment**

#### **7.1 Documentation Updates**
- Update CLAUDE.md for 5-step architecture
- Update API documentation

## **Critical Success Metrics**

2. **Backward Compatibility**: Existing sessions continue to function
3. **Performance Maintained**: No degradation in form auto-save or navigation
4. **Type Safety**: 100% TypeScript compliance maintained
5. **Test Coverage**: Comprehensive test suite for all new components

This production-ready plan ensures a seamless transition from 4-step to 5-step architecture while maintaining all existing functionality and improving the overall system architecture.