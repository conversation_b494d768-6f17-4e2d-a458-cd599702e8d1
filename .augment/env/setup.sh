#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS) and npm
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
node --version
npm --version

# Navigate to project directory
cd /mnt/persist/workspace

# Install project dependencies
npm install

# Add npm global bin to PATH in user profile
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> $HOME/.profile
mkdir -p $HOME/.npm-global
npm config set prefix $HOME/.npm-global

# Source the profile to make PATH available immediately
source $HOME/.profile

# Verify installation by checking if vitest is available
npx vitest --version

echo "Setup completed successfully!"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Project dependencies installed"