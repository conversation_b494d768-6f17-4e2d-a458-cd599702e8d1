/*
--------------------------------------------------------------------------------
-- File: farms_schema_RLS_policies.sql
--
-- Version: 3.1 - Updated for complete schema coverage
--
-- Description:
-- Complete and robust RLS policies for the hierarchical farms schema.
-- Includes all tables and handles the key_staff table addition.
--------------------------------------------------------------------------------
*/

--==============================================================================
-- 0. GRANT SCHEMA AND TABLE ACCESS TO SUPABASE ROLES
-- These grants are required before RLS can be applied
--==============================================================================

-- Grant schema usage to Supabase roles
GRANT USAGE ON SCHEMA farms TO authenticated;
GRANT USAGE ON SCHEMA farms TO anon;
GRANT USAGE ON SCHEMA farms TO service_role;

-- Grant table permissions to roles
GRANT ALL ON ALL TABLES IN SCHEMA farms TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA farms TO service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA farms TO anon;

-- Grant usage on custom types
GRANT USAGE ON TYPE farms.activity_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.agreement_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.contact_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.business_structure_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.license_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.asset_category_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.communication_method_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.reporting_frequency_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.bas_frequency_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.authority_level_enum TO authenticated, service_role;

-- Grant usage on enhanced enum types
GRANT USAGE ON TYPE farms.accounting_method_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.chart_of_accounts_setup_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.bas_preparation_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.employment_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.supplier_type_enum TO authenticated, service_role;

-- Grant usage on updated custom types for Step 4
GRANT USAGE ON TYPE farms.license_status_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.renewal_frequency_enum TO authenticated, service_role;

-- Grant usage on Step 4 enum types
GRANT USAGE ON TYPE farms.vehicle_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.insurance_type_enum TO authenticated, service_role;

--==============================================================================
-- 1. ROOT TABLE POLICIES: 'onboarding_sessions'
-- These policies are the foundation of the entire security model.
--==============================================================================

ALTER TABLE farms.onboarding_sessions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Onboarding Sessions: Users can manage their own sessions" ON farms.onboarding_sessions;
CREATE POLICY "Onboarding Sessions: Users can manage their own sessions"
ON farms.onboarding_sessions
FOR ALL
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Onboarding Sessions: Prevent deletion of completed sessions" ON farms.onboarding_sessions;
CREATE POLICY "Onboarding Sessions: Prevent deletion of completed sessions"
ON farms.onboarding_sessions
FOR DELETE
USING (status <> 'completed' AND user_id = auth.uid());

--==============================================================================
-- 2. PARENT "STEP" TABLE POLICIES
-- These policies secure the four main parent tables. Access is granted if the
-- user owns the referenced 'onboarding_sessions' record.
--==============================================================================

-- Table: step_1_business_profile
ALTER TABLE farms.step_1_business_profile ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 1: Full access for session owners" ON farms.step_1_business_profile;
CREATE POLICY "Step 1: Full access for session owners" ON farms.step_1_business_profile FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_2_farm_operations
ALTER TABLE farms.step_2_farm_operations ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 2: Full access for session owners" ON farms.step_2_farm_operations;
CREATE POLICY "Step 2: Full access for session owners" ON farms.step_2_farm_operations FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_3_financial_systems
ALTER TABLE farms.step_3_financial_systems ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 3: Full access for session owners" ON farms.step_3_financial_systems;
CREATE POLICY "Step 3: Full access for session owners" ON farms.step_3_financial_systems FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_4_agreements
ALTER TABLE farms.step_4_agreements ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 4: Full access for session owners" ON farms.step_4_agreements;
CREATE POLICY "Step 4: Full access for session owners" ON farms.step_4_agreements FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Enhanced policies for Step 2 new tables
DO $$
BEGIN
    -- farm_info table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'farm_info') THEN
        ALTER TABLE farms.farm_info ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Farm Info: Full access for session owners" ON farms.farm_info;
        CREATE POLICY "Farm Info: Full access for session owners" ON farms.farm_info FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));
    END IF;

    -- farm_blocks table policies  
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'farm_blocks') THEN
        ALTER TABLE farms.farm_blocks ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Farm Blocks: Full access for session owners" ON farms.farm_blocks;
        CREATE POLICY "Farm Blocks: Full access for session owners" ON farms.farm_blocks FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));
    END IF;
END $$;

--==============================================================================
-- 3. CHILD TABLE POLICIES
-- These policies use a subquery to join up the hierarchy to verify ownership.
--==============================================================================

-- STEP 1 CHILDREN
ALTER TABLE farms.business_registration ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Business Registration: Full access for session owners" ON farms.business_registration;
CREATE POLICY "Business Registration: Full access for session owners" ON farms.business_registration FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

ALTER TABLE farms.addresses ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Addresses: Full access for session owners" ON farms.addresses;
CREATE POLICY "Addresses: Full access for session owners" ON farms.addresses FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

ALTER TABLE farms.contacts ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Contacts: Full access for session owners" ON farms.contacts;
CREATE POLICY "Contacts: Full access for session owners" ON farms.contacts FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

-- Key Staff table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'key_staff') THEN
        ALTER TABLE farms.key_staff ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Key Staff: Full access for session owners" ON farms.key_staff;
        CREATE POLICY "Key Staff: Full access for session owners" ON farms.key_staff FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));
    END IF;
END $$;

-- STEP 2 CHILDREN
ALTER TABLE farms.activities ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Activities: Full access for session owners" ON farms.activities;
CREATE POLICY "Activities: Full access for session owners" ON farms.activities FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.licenses ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Licenses: Full access for session owners" ON farms.licenses;
CREATE POLICY "Licenses: Full access for session owners" ON farms.licenses FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.suppliers ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Suppliers: Full access for session owners" ON farms.suppliers;
CREATE POLICY "Suppliers: Full access for session owners" ON farms.suppliers FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.contracts ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Contracts: Full access for session owners" ON farms.contracts;
CREATE POLICY "Contracts: Full access for session owners" ON farms.contracts FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.chemical_usage ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Chemical Usage: Full access for session owners" ON farms.chemical_usage;
CREATE POLICY "Chemical Usage: Full access for session owners" ON farms.chemical_usage FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

-- STEP 3 CHILDREN
ALTER TABLE farms.bookkeeping ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Bookkeeping: Full access for session owners" ON farms.bookkeeping;
CREATE POLICY "Bookkeeping: Full access for session owners" ON farms.bookkeeping FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

ALTER TABLE farms.payroll ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Payroll: Full access for session owners" ON farms.payroll;
CREATE POLICY "Payroll: Full access for session owners" ON farms.payroll FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

ALTER TABLE farms.assets ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Assets: Full access for session owners" ON farms.assets;
CREATE POLICY "Assets: Full access for session owners" ON farms.assets FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

-- STEP 4 CHILDREN
ALTER TABLE farms.data_migration ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Data Migration: Full access for session owners" ON farms.data_migration;
CREATE POLICY "Data Migration: Full access for session owners" ON farms.data_migration FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.permissions ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Permissions: Full access for session owners" ON farms.permissions;
CREATE POLICY "Permissions: Full access for session owners" ON farms.permissions FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.agreements ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Agreements: Full access for session owners" ON farms.agreements;
CREATE POLICY "Agreements: Full access for session owners" ON farms.agreements FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.payments ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Payments: Full access for session owners" ON farms.payments;
CREATE POLICY "Payments: Full access for session owners" ON farms.payments FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.communication_preferences ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Comm Preferences: Full access for session owners" ON farms.communication_preferences;
CREATE POLICY "Comm Preferences: Full access for session owners" ON farms.communication_preferences FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));


--==============================================================================
-- 4. CENTRAL DOCUMENTS TABLE POLICY
-- This policy remains simple as it's a direct child of onboarding_sessions.
--==============================================================================
ALTER TABLE farms.documents ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Documents: Full access for session owners" ON farms.documents;
CREATE POLICY "Documents: Full access for session owners" ON farms.documents FOR ALL
TO authenticated, service_role
USING (
    -- Allow service role for Edge Functions (they perform their own auth checks)
    auth.role() = 'service_role' OR
    -- Regular users can only access documents from their own sessions
    EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid())
)
WITH CHECK (
    -- Allow service role for Edge Functions (they perform their own auth checks)
    auth.role() = 'service_role' OR
    -- Regular users can only create/update documents in their own sessions
    EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid())
);

-- Add missing RLS policies for Step 4 parent table
ALTER TABLE farms.step_4_registrations_insurance ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 4 Registrations: Full access for session owners" ON farms.step_4_registrations_insurance;
CREATE POLICY "Step 4 Registrations: Full access for session owners" ON farms.step_4_registrations_insurance FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Add missing RLS policies for Step 3 new tables
DO $$
BEGIN
    -- external_accountant table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'external_accountant') THEN
        ALTER TABLE farms.external_accountant ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "External Accountant: Full access for session owners" ON farms.external_accountant;
        CREATE POLICY "External Accountant: Full access for session owners" ON farms.external_accountant FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));
    END IF;

    -- banking_info table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'banking_info') THEN
        ALTER TABLE farms.banking_info ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Banking Info: Full access for session owners" ON farms.banking_info;
        CREATE POLICY "Banking Info: Full access for session owners" ON farms.banking_info FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));
    END IF;

    -- financial_reporting_config table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'financial_reporting_config') THEN
        ALTER TABLE farms.financial_reporting_config ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Financial Reporting Config: Full access for session owners" ON farms.financial_reporting_config;
        CREATE POLICY "Financial Reporting Config: Full access for session owners" ON farms.financial_reporting_config FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));
    END IF;

    -- integration_settings table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'integration_settings') THEN
        ALTER TABLE farms.integration_settings ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Integration Settings: Full access for session owners" ON farms.integration_settings;
        CREATE POLICY "Integration Settings: Full access for session owners" ON farms.integration_settings FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));
    END IF;
END $$;

-- Add missing RLS policies for Step 4 new tables
DO $$
BEGIN
    -- vehicle_registrations table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'vehicle_registrations') THEN
        ALTER TABLE farms.vehicle_registrations ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Vehicle Registrations: Full access for session owners" ON farms.vehicle_registrations;
        CREATE POLICY "Vehicle Registrations: Full access for session owners" ON farms.vehicle_registrations FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));
    END IF;

    -- insurance_policies table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'insurance_policies') THEN
        ALTER TABLE farms.insurance_policies ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Insurance Policies: Full access for session owners" ON farms.insurance_policies;
        CREATE POLICY "Insurance Policies: Full access for session owners" ON farms.insurance_policies FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));
    END IF;

    -- licenses_new table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'licenses_new') THEN
        ALTER TABLE farms.licenses_new ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Licenses New: Full access for session owners" ON farms.licenses_new;
        CREATE POLICY "Licenses New: Full access for session owners" ON farms.licenses_new FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_registrations_insurance s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));
    END IF;
END $$;

-- Add missing RLS policies for Step 5 parent table
ALTER TABLE farms.step_5_finalization ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 5 Finalization: Full access for session owners" ON farms.step_5_finalization;
CREATE POLICY "Step 5 Finalization: Full access for session owners" ON farms.step_5_finalization FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Add comprehensive RLS policies for Step 5 child tables
DO $$
BEGIN
    -- data_migration table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'data_migration') THEN
        ALTER TABLE farms.data_migration ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Data Migration: Full access for session owners" ON farms.data_migration;
        CREATE POLICY "Data Migration: Full access for session owners" ON farms.data_migration FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
    END IF;

    -- permissions table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'permissions') THEN
        ALTER TABLE farms.permissions ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Permissions: Full access for session owners" ON farms.permissions;
        CREATE POLICY "Permissions: Full access for session owners" ON farms.permissions FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
    END IF;

    -- agreements table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'agreements') THEN
        ALTER TABLE farms.agreements ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Agreements: Full access for session owners" ON farms.agreements;
        CREATE POLICY "Agreements: Full access for session owners" ON farms.agreements FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
    END IF;

    -- payments table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'payments') THEN
        ALTER TABLE farms.payments ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Payments: Full access for session owners" ON farms.payments;
        CREATE POLICY "Payments: Full access for session owners" ON farms.payments FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
    END IF;

    -- communication_preferences table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'communication_preferences') THEN
        ALTER TABLE farms.communication_preferences ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Communication Preferences: Full access for session owners" ON farms.communication_preferences;
        CREATE POLICY "Communication Preferences: Full access for session owners" ON farms.communication_preferences FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
    END IF;

    -- finalization_submission table policies
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'finalization_submission') THEN
        ALTER TABLE farms.finalization_submission ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Finalization Submission: Full access for session owners" ON farms.finalization_submission;
        CREATE POLICY "Finalization Submission: Full access for session owners" ON farms.finalization_submission FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_5_finalization s5 JOIN farms.onboarding_sessions os ON s5.onboarding_session_id = os.id WHERE s5.id = step_5_id AND os.user_id = auth.uid()));
        
        -- Special policy for service role to update processing status
        DROP POLICY IF EXISTS "Finalization Submission: Service role processing updates" ON farms.finalization_submission;
        CREATE POLICY "Finalization Submission: Service role processing updates" ON farms.finalization_submission FOR UPDATE
        TO service_role
        USING (true)
        WITH CHECK (true);
    END IF;
END $$;

/*
--------------------------------------------------------------------------------
-- END OF ENHANCED RLS POLICIES V3.1
-- 
-- Key improvements:
-- 1. Added WITH CHECK clauses to all policies for complete INSERT/UPDATE security
-- 2. Added support for key_staff table with conditional creation check
-- 3. Added grants for custom enum types
-- 4. Enhanced policy names for clarity
-- 5. Comprehensive coverage of all schema tables
--------------------------------------------------------------------------------
*/
